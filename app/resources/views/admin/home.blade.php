@extends('layouts.admin')

<style type="text/css">
    .bd-dark {
        border: 1px solid #000 !important;
    }

    .btn-select {
        font-style: italic;
        color: #1c7430 !important;
    }

    .meubg {
        background-color: var(--primary-color);
    }

    .meubg2 {
        background-color: var(--light-bg);
    }

    /* Enhanced chart styling */
    .chart-container {
        height: 350px !important;
        width: 100%;
        position: relative;
        border-radius: 8px;
        padding: 10px;
        transition: all 0.3s ease;
    }

    .chart-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        height: 450px;
    }

    .chart-card:hover {
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        transform: translateY(-5px);
    }

    .chart-card .dashboard-card-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .chart-card .dashboard-card-header h4 {
        font-weight: 600;
    }

    /* Premios chart card styling */
    .premios-chart-card .dashboard-card-body {
        padding: 1.5rem;
    }

    .premio-value-container {
        display: inline-block;
        position: relative;
        margin: 0 auto;
        padding: 1.5rem;
        background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
        min-width: 180px;
    }

    .premio-value {
        font-size: 3.5rem;
        font-weight: 700;
        color: #435887;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: block;
        position: relative;
    }

    .premio-value::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background-color: #435887;
        border-radius: 3px;
    }

    .premio-label {
        font-size: 1.1rem;
        color: #333;
        margin-top: 1.2rem;
        font-weight: 600;
    }

    .premio-progress-container {
        margin-top: 1.5rem;
        padding: 1.25rem;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .premio-progress-container p,
    .premio-progress-container span {
        color: #333;
        font-size: 0.95rem;
    }

    .premio-progress {
        height: 1.5rem;
        border-radius: 10px;
        background-color: rgba(0, 0, 0, 0.05);
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        margin: 0.75rem 0;
    }

    .premio-progress .progress-bar {
        background-color: var(--success-color);
        background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%,
            transparent 75%,
            transparent);
        background-size: 1rem 1rem;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        transition: width 1s ease;
        border-radius: 10px;
    }

    /* Vendas chart card styling */
    .vendas-chart-card {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        border: none;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .vendas-chart-card .dashboard-card-header {
        background-color: rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .vendas-chart-card .dashboard-card-header h4 {
        color: #fff;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .vendas-chart-card .dashboard-card-body {
        padding: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .vendas-chart-title {
        text-align: center;
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 1rem;
        font-weight: 500;
    }

    /* Programa Fidelidade specific styling */
    #grafico_programa {
        background: white !important;
        width: 100% !important;
    }

    .programa-fidelidade-card .dashboard-card-body {
        padding: 0.5rem;
    }

    /* Chart actions styling */
    .dashboard-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-actions {
        display: flex;
        gap: 8px;
    }

    .chart-action-btn {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        transition: all 0.2s ease;
        background-color: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .chart-action-btn:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .programa-fidelidade-card {
        border-left: none;
    }

    /* Estilos para os cards finais */
    .dashboard-row .container-fluid .row {
        margin-top: 1.25rem;
    }

    .dashboard-row .stat-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    /* Estilos para distribuição igual dos cards */
    .dashboard-row .row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 1.25rem;
    }

    .dashboard-row .row > div {
        display: flex;
        flex-direction: column;
    }

    @media (min-width: 992px) {
        .dashboard-row .row > div {
            flex: 1 0 0;
            max-width: 100%;
        }
    }

    /* Estilos para os cards principais */
    .row.mb-4 {
        margin-left: -0.75rem;
        margin-right: -0.75rem;
        margin-bottom: 1.25rem !important;
    }

    .row.mb-4 > .col-lg-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        margin-bottom: 1.25rem;
    }

    /* Estilos para o card Lançar Ponto */
    .dashboard-card.card-primary {
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .dashboard-card .input-group {
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .dashboard-card .input-group-prepend .input-group-text {
        border-radius: var(--border-radius) 0 0 var(--border-radius);
    }

    .dashboard-card .input-group .form-control {
        border-radius: 0 var(--border-radius) var(--border-radius) 0;
    }

    .dashboard-card .btn-outline-light {
        border-radius: calc(var(--border-radius) / 2);
    }

    .dashboard-card .form-control {
        border-radius: var(--border-radius);
    }

    .dashboard-card .btn-light {
        border-radius: var(--border-radius);
    }

    .dashboard-card {
        transition: all 0.3s ease;
        border-radius: var(--border-radius);
        min-height: 350px;
        margin-bottom: 1rem;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .dashboard-card-header {
        padding: 1rem 1.25rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .dashboard-card-body {
        padding: 1.25rem;
    }

    /* WincashAi card styling */
    .ai-icon i {
        transition: all 0.5s ease;
    }

    .dashboard-card:hover .ai-icon i {
        transform: scale(1.1) rotate(5deg);
    }

    .ai-features .badge {
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .ai-features .badge:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    /* Improved table styling */
    .dashboard-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .dashboard-table thead th {
        background-color: rgba(0,0,0,0.05);
        padding: 0.75rem;
        font-weight: 600;
        border-bottom: 2px solid rgba(0,0,0,0.1);
    }

    .dashboard-table tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .dashboard-table tbody tr:hover {
        background-color: rgba(0,0,0,0.02);
    }

    .dashboard-table tbody tr.hover-effect:hover {
        background-color: rgba(0,0,0,0.02);
        transition: background-color 0.2s ease;
    }

    .table-scrollable {
        max-height: 300px;
        overflow-y: auto;
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.05);
    }

    /* Estilos para os cards de atendente e pontuação */
    .filter-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 1rem;
    }

    .filter-button {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        border: none;
        background-color: #f8f9fa;
    }

    .filter-button.btn-light {
        background-color: #f8f9fa;
        color: #495057;
    }

    .filter-button.btn-light:hover {
        background-color: #e9ecef;
        color: #212529;
    }

    .filter-button.btn-select {
        background-color: var(--primary-color);
        color: white;
    }

    .dashboard-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .dashboard-table thead th {
        padding: 0.75rem;
        font-weight: 600;
        border-bottom: 2px solid rgba(0,0,0,0.1);
        background-color: rgba(0,0,0,0.03);
    }

    .dashboard-table tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    /* Fuel summary styling */
    .fuel-summary {
        transition: all 0.3s ease;
    }

    .total-fuel-container {
        transition: all 0.3s ease;
    }

    .total-fuel-container:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .total-fuel-label {
        display: block;
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.5rem;
    }

    .font-weight-medium {
        font-weight: 500;
    }

    /* Filter buttons styling */
    .filter-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .filter-button {
        padding: 0.5rem 1rem;
        background-color: rgba(0,0,0,0.05);
        border: none;
        border-radius: 4px;
        transition: all 0.2s ease;
        font-size: 0.9rem;
    }

    .filter-button:hover {
        background-color: var(--primary-color);
        color: white;
        opacity: 0.9; /* Garante que o botão não fique invisível */
    }

    @media (max-width: 576px) {
        .filter-buttons {
            flex-direction: column;
        }

        .filter-button {
            width: 100%;
            margin-right: 0;
        }
    }

    /* Estilos específicos para os botões da tela de pontos */
    .btn-outline-light {
        color: #fff !important;
        border-color: #fff !important;
        background: transparent !important;
    }

    .btn-outline-light:hover {
        color: var(--primary-color) !important;
        background: #fff !important;
        opacity: 1 !important;
    }

    .dashboard-card .btn-light {
        background-color: #fff !important;
        border-color: #fff !important;
        color: var(--primary-color) !important;
    }

    .dashboard-card .btn-light:hover {
        background-color: #f8f9fa !important;
        opacity: 1 !important;
    }

    .dashboard-card .btn-primary {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: #fff !important;
    }

    .dashboard-card .btn-primary:hover {
        opacity: 0.9 !important;
    }

    /* Card Colors */
    .card-primary {
      background: #435887;
      color: var(--text-light);
    }

    .card-secondary {
      background: linear-gradient(135deg, var(--secondary-color), #560bad);
      color: var(--text-light);
    }

    .card-accent {
      background: linear-gradient(135deg, var(--accent-color), #b5179e);
      color: var(--text-light);
    }

    .card-danger {
      background: linear-gradient(135deg, var(--danger-color), #ef233c);
      color: var(--text-light);
    }

    .card-warning {
      background: linear-gradient(135deg, var(--warning-color), #ffb703);
      color: var(--dark-bg);
    }

    .card-info {
      background: linear-gradient(135deg, var(--info-color), #00b4d8);
      color: var(--text-light);
    }

    .card-success {
      background: linear-gradient(135deg, var(--success-color), #2dc653);
      color: var(--text-light);
    }

    .card-light {
      background: linear-gradient(135deg, var(--light-bg), #e9ecef);
      color: var(--text-color);
    }

    /* Override for Programa Fidelidade card */
    .card-light.programa-fidelidade-card {
      background: white !important;
      background-image: none !important;
      color: var(--text-color) !important;
    }

    .card-light.programa-fidelidade-card .dashboard-card-header,
    .card-light.programa-fidelidade-card .dashboard-card-body,
    .card-light.programa-fidelidade-card .chart-container {
      background: white !important;
      background-image: none !important;
    }

    .card-dark {
      background: linear-gradient(135deg, var(--dark-bg), #111827);
      color: var(--text-light);
    }

    /* Override for Premio chart card to be white */
    .card-light.premios-chart-card {
      background: white !important;
      background-image: none !important;
      color: var(--text-color) !important;
    }

    .card-light.premios-chart-card .dashboard-card-header,
    .card-light.premios-chart-card .dashboard-card-body {
      background: white !important;
      background-image: none !important;
    }

    /* Dashboard layout spacing */
    .dashboard-container {
        padding: 0.75rem;
    }

    .dashboard-row {
        margin-bottom: 1.5rem;
    }

    .col-md-6.mb-4 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .dashboard-row .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .dashboard-row .container-fluid .row .col-lg {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Dashboard layout spacing - standardized */
    .dashboard-container {
        padding: 1rem;
    }

    .row {
        margin-bottom: 1rem; /* Removed bottom margin from rows */
    }

    .px-2 {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .dashboard-row {
        margin-bottom: 0; /* Also removed from dashboard-row for consistency */
    }

    .dashboard-card {
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    }

    .col-lg-6, .col-md-6, .col-lg-4, .col-lg-8, .col-lg-12 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        margin-bottom: 0; /* Removed margin from columns, letting cards control spacing */
    }

    .dashboard-row .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .dashboard-row .container-fluid .row {
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }

    .dashboard-row .container-fluid .row > div {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Remove conflicting margins */
    .row.mb-4 {
        margin-bottom: 0 !important; /* Changed from 1.5rem to 0 */
    }
</style>

@section('content')
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript" src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>

    <div class="dashboard-container">

        @if(count($unidades) > 0)
            <div class="form-group col-auto mb-3">

                <form action="{{route('admin')}}" method="GET" id="form-filtro-admin" role="form"
                      class="form form-inline formloading">

                    <select name="unidades_id" id="unidades_id" class="form-control w-auto" onchange="$('#form-filtro-admin').submit()">
                        <option value="">-- Todas as Unidades --</option>
                        @foreach($unidades as $unidade)
                            <option value="{{$unidade->id}}"
                                    @if($unidades_id == $unidade->id)selected="selected" @endif >
                                {{$unidade->nome}}
                            </option>
                        @endforeach
                    </select>
                </form>

            </div>
        @endif

        <div class="row">
            <div class="container-fluid px-2">
                <div class="row">
                    <div class="col-6 col-sm-4 col-md-auto col-lg px-2 flex-grow-1">
                        <div class="dashboard-card card-primary stat-card card-pontos">
                            <div class="stat-card-icon">
                                <i class="fa fa-calendar-alt"></i>
                            </div>
                            <h4>Pontos</h4>
                            <div class="stat-card-value">
                                @if(isset($totalmes[1]['pontos']) && isset($totalmes[0]['pontos']))
                                    <span>{{number_format($totalmes[0]['pontos'], 0, '', '')}}</span>
                                @else
                                    <span>--</span>
                                @endif
                            </div>
                            <div class="stat-card-label">Este mês</div>
                        </div>
                    </div>

                    <div class="col-6 col-sm-4 col-md-auto col-lg px-2 flex-grow-1">
                        <div class="dashboard-card card-secondary stat-card card-cadastros">
                            <div class="stat-card-icon">
                                <i class="fa fa-users"></i>
                            </div>
                            <h4>Cadastros</h4>
                            <div class="stat-card-value">
                                @if(isset($totalcadastros[0]['qtd']))
                                    <span>{{number_format($totalcadastros[0]['qtd'], 0, '', '')}}</span>
                                @else
                                    <span>--</span>
                                @endif
                            </div>
                            <div class="stat-card-label">Este mês</div>
                        </div>
                    </div>

                    <div class="col-6 col-sm-4 col-md-auto col-lg px-2 flex-grow-1">
                        <div class="dashboard-card card-success stat-card card-resgates">
                            <div class="stat-card-icon">
                                <i class="fa fa-trophy"></i>
                            </div>
                            <h4>Resgates</h4>
                            <div class="stat-card-value">
                                @if(isset($totalpremio[0]['pontos']))
                                    <span>{{number_format($totalpremio[0]['pontos'], 0, '', '')}}</span>
                                @else
                                    <span>--</span>
                                @endif
                            </div>
                            <div class="stat-card-label">Este mês</div>
                        </div>
                    </div>

                    <div class="col-6 col-sm-6 col-md-auto col-lg px-2 flex-grow-1">
                        <div class="dashboard-card card-info stat-card card-valor">
                            <div class="stat-card-icon">
                                <i class="fa fa-award"></i>
                            </div>
                            <h4>Valor</h4>
                            <div class="stat-card-value">
                                <span class="currency">R$</span>{{number_format($valorcompras,0,'','')}}
                            </div>
                            <div class="stat-card-label">no mês</div>
                        </div>
                    </div>

                    <div class="col-6 col-sm-6 col-md-auto col-lg px-2 flex-grow-1">
                        <div class="dashboard-card card-warning stat-card card-total">
                            <div class="stat-card-icon">
                                <i class="fa fa-money-check-alt"></i>
                            </div>
                            <h4>Total</h4>
                            <div class="stat-card-value">
                                <span class="currency">R$</span>{{number_format($somaano,0,'','')}}
                            </div>
                            <div class="stat-card-label">em {{date('Y')}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 px-2">
                <div class="dashboard-card card-primary h-100 shadow">
                    <div class="dashboard-card-header d-flex justify-content-between align-items-center">
                        <h4 class="text-white mb-0"><i class="fa fa-award mr-2" style="color: #ffffff;"></i> Lançar Ponto</h4>
                        <a href="{{route('pontos-baixar')}}" class="btn btn-outline-light btn-sm">
                            <i class="fa fa-minus-circle mr-1"></i> Baixar Resgate
                        </a>
                    </div>
                    <div class="dashboard-card-body">
                        <form action="{{route('pontos-adicionar')}}" method="POST" id="form-premios" role="form"
                            class="form formloading">
                            {{ csrf_field() }}

                            @if(Session::get('logado') == 'gestor')
                                <div class="form-group">
                                    <label for="clientes_id" class="text-white-50 small font-weight-bold">Cliente</label>
                                    <select name="clientes_id" id="clientes_id" class="form-control form-control-sm" required="required">
                                        <option value="">-- Selecione o cliente --</option>
                                        @foreach($clientes as $cliente)
                                            <option value="{{$cliente->id}}">
                                                {{$cliente->nome}}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif

                            @if(Session::get('logado') != 'gestor' and isset($cadastros) and count($cadastros))
                                <div class="form-group d-flex justify-content-end mb-2">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" name="tipobusca" value="nome" class="custom-control-input tipobusca" id="tipobuscaSwitch">
                                        <label class="custom-control-label text-white-50 small" for="tipobuscaSwitch">Lançar por nome/celular</label>
                                    </div>
                                </div>

                                <div class="form-group" id="inputcadastros" style="display: none">
                                    <label for="cadastros_id" class="text-white-50 small font-weight-bold">Consumidor</label>
                                    <select name="cadastros_id" id="cadastros_id"
                                            class="form-control form-control-sm selectpicker"
                                            data-live-search="true"
                                            data-selected-text-format="count"
                                            data-actions-box="true"
                                            data-style="btn-light"
                                    >
                                        <option value="">-- Selecione o consumidor --</option>
                                        @foreach($cadastros as $cadastro)
                                            <option value="{{$cadastro->cadastronacional}}">
                                                {{$cadastro->nome}} - {{$cadastro->celular}}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif

                            <div class="form-group mt-3">
                                <label for="cadastronacional" class="text-white-50 small font-weight-bold">CPF ou CNPJ</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-white border-right-0">
                                            <i class="fa fa-id-card text-primary"></i>
                                        </span>
                                    </div>
                                    <input type="text" name="cadastronacional" class="form-control form-control-lg border-left-0"
                                        id="cadastronacional" placeholder="Digite o CPF ou CNPJ" required="required" pattern="[0-9]*"/>
                                </div>
                            </div>

                            <div class="form-group input-loading text-center mt-4">
                                <button type="submit" class="btn btn-light btn-lg px-4">
                                    <i class="fa fa-check-circle mr-2"></i> Processar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 px-2">
                <div class="dashboard-card h-100 shadow" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header d-flex justify-content-between align-items-center" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4 class="mb-0"><i class="fa fa-calendar-alt mr-2" style="color: #435887;"></i> Últimos pontos</h4>
                        <div class="chart-actions">
                            <button class="btn btn-sm btn-outline-secondary chart-action-btn" id="refresh-ultimos-pontos" title="Atualizar" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                <i class="fa fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-card-body" style="background: white !important; padding: 1.25rem;">
                        <div class="table-scrollable border-0">
                            <table class="dashboard-table table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 15%;">Data</th>
                                        @if($permissao == 'gestor')
                                            <th style="width: 20%;">Cliente</th>
                                        @endif
                                        <th style="width: 45%;">Consumidor</th>
                                        <th style="width: 20%; text-align: right;">Pontos</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($aprovadas as $pontuacao)
                                        <tr>
                                            <td style="padding: 0.75rem;">
                                                <span class="badge badge-light" style="background-color: rgba(0,0,0,0.05); color: var(--text-color); font-weight: 500;">{{$pontuacao->dtcompra->format('d/m/Y')}}</span>
                                            </td>
                                            @if($permissao == 'gestor')
                                                <td style="padding: 0.75rem;"><span style="color: var(--text-color)">{{$pontuacao->cliente}}</span></td>
                                            @endif
                                            <td style="padding: 0.75rem;">
                                                <div class="d-flex flex-column">
                                                    <span class="font-weight-medium" style="color: var(--text-color)">{{$pontuacao->consumidor}}</span>
                                                    @if($pontuacao->premios_id != null)
                                                        <small class="text-muted">{{Str::limit($pontuacao->premio->titulo, 40)}}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="text-right" style="padding: 0.75rem;">
                                                <div class="d-flex flex-column align-items-end">
                                                    <span class="font-weight-bold" style="color: var(--primary-color)">{{number_format($pontuacao->pontuacao, 0, '', '.')}}</span>
                                                    @if($pontuacao->premios_id != null)
                                                        <small class="badge badge-light" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">{{$pontuacao->voucher}}</small>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="dashboard-card-footer text-center text-muted" style="background: white !important; border-top: 1px solid rgba(0,0,0,0.05); font-size: 0.8rem;">
                        &nbsp;
                    </div>
                </div>
            </div>


        </div>

        <div class="row">
            <div class="col-lg-6 px-2">
                <div class="dashboard-card h-100 shadow" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header d-flex justify-content-between align-items-center" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4 class="mb-0"><i class="fa fa-user-plus mr-2" style="color: #435887;"></i> <span id="txt-cadastro">Cadastros por atendente</span></h4>
                        <div class="chart-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary chart-action-btn dropdown-toggle" type="button" id="dropdownCadastros" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Exportar dados" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                    <i class="fa fa-download"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownCadastros">
                                    <a class="dropdown-item" href="#" id="download-cadastros-chart">
                                        <i class="fa fa-image mr-2"></i> Baixar PNG
                                    </a>
                                    <a class="dropdown-item" href="#" id="export-cadastros-csv">
                                        <i class="fa fa-file-csv mr-2"></i> Exportar CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card-body" style="background: white !important;">
                        <div class="filter-buttons mb-3">
                            <button id="btn_cad_hoje" class="filter-button btn-light">
                                <i class="fa fa-calendar-day"></i> Hoje
                            </button>
                            <button id="btn_cad_semana" class="filter-button btn-light">
                                <i class="fa fa-calendar-week"></i> Semana
                            </button>
                            <button id="btn_cad_mes" class="filter-button btn-light">
                                <i class="fa fa-calendar-alt"></i> Mês
                            </button>
                            <button id="btn_cad_outro" class="filter-button btn-light">
                                <i class="fa fa-calendar-plus"></i> Personalizado
                            </button>

                            <div id="div-filter-cadastro" style="position: fixed; z-index: 9999; margin-top: 40px; right: 20px; width: 250px" class="dashboard-card card-light p-3 d-none shadow">
                                <div class="form-group mb-2">
                                    <label for="input-datai-cadastro" class="small font-weight-bold">Data Inicial</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-datai-cadastro" class="form-control" type="date" />
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="input-dataf-cadastro" class="small font-weight-bold">Data Final</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-dataf-cadastro" class="form-control" type="date" />
                                    </div>
                                </div>

                                <button id="btn-filter-cadastro-data" class="btn btn-primary btn-block">
                                    <i class="fa fa-filter mr-1"></i> Filtrar
                                </button>
                            </div>
                        </div>

                        {{ csrf_field() }}

                        @if(Session::get('logado') == 'gestor')
                            <div class="form-group mb-3">
                                <label for="clientes_id_cadastro" class="small font-weight-bold">Cliente</label>
                                <select id="clientes_id_cadastro" name="clientes_id" class="form-control form-control-sm" required="required">
                                    <option value="">-- Todos os clientes --</option>
                                    @foreach($clientes as $cliente)
                                        <option value="{{$cliente->id}}">
                                            {{$cliente->nome}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <div class="table-scrollable border-0">
                            <table class="dashboard-table table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 70%;">Atendente</th>
                                        <th style="width: 30%; text-align: right">Cadastros</th>
                                    </tr>
                                </thead>
                                <tbody id="dados_cad_atendente">
                                    @if(!empty($cadastroporatendente))
                                        @php
                                            $totalCadastros = 0;
                                            foreach($cadastroporatendente as $cadastro) {
                                                $totalCadastros += $cadastro['qtd'];
                                            }

                                            // Convert to array if it's a collection and sort by quantity in descending order
                                            $sortedCadastros = is_array($cadastroporatendente) ? $cadastroporatendente : $cadastroporatendente->toArray();
                                            usort($sortedCadastros, function($a, $b) {
                                                return intval($b['qtd']) - intval($a['qtd']);
                                            });
                                        @endphp

                                        @foreach($sortedCadastros as $cadastro)
                                            @php
                                                $percentagem = $totalCadastros > 0 ? ($cadastro['qtd'] / $totalCadastros) * 100 : 0;
                                                $isTopPerformer = $percentagem > 30;
                                            @endphp
                                            <tr class="{{ $isTopPerformer ? 'table-success' : '' }}">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: {{ $isTopPerformer ? 'var(--secondary-color)' : '#6c757d' }}; color: white; border-radius: 4px;">
                                                            <i class="fa {{ $isTopPerformer ? 'fa-award' : 'fa-user' }} fa-sm"></i>
                                                        </span>
                                                        <span class="font-weight-medium" style="color: var(--text-color)">{{!empty($cadastro['nome']) ? $cadastro['nome'] : 'Sem vendedor'}}</span>
                                                    </div>
                                                </td>
                                                <td style="text-align: right">
                                                    <div class="d-flex flex-column align-items-end">
                                                        <span class="badge {{ $isTopPerformer ? 'badge-success' : 'badge-secondary' }} p-2">{{number_format($cadastro['qtd'], 0, '', '.')}}</span>
                                                        @if($totalCadastros > 0)
                                                            <small class="text-muted mt-1">{{number_format($percentagem, 1)}}%</small>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                        <tr class="bg-light font-weight-bold">
                                            <td>TOTAL</td>
                                            <td style="text-align: right">{{number_format($totalCadastros, 0, '', '.')}}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 px-2">
                <div class="dashboard-card h-100 shadow" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header d-flex justify-content-between align-items-center" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4 class="mb-0"><i class="fa fa-chart-line mr-2" style="color: #435887;"></i> <span id="txt-pontuacao">Pontuação por atendente</span></h4>
                        <div class="chart-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary chart-action-btn dropdown-toggle" type="button" id="dropdownPontuacao" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Exportar dados" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                    <i class="fa fa-download"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownPontuacao">
                                    <a class="dropdown-item" href="#" id="download-pontuacao-chart">
                                        <i class="fa fa-image mr-2"></i> Baixar PNG
                                    </a>
                                    <a class="dropdown-item" href="#" id="export-pontuacao-csv">
                                        <i class="fa fa-file-csv mr-2"></i> Exportar CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card-body" style="background: white !important;">
                        <div class="filter-buttons mb-3">
                            <button id="btn_pontos_hoje" class="filter-button btn-light">
                                <i class="fa fa-calendar-day"></i> Hoje
                            </button>
                            <button id="btn_pontos_semana" class="filter-button btn-light">
                                <i class="fa fa-calendar-week"></i> Semana
                            </button>
                            <button id="btn_pontos_mes" class="filter-button btn-light">
                                <i class="fa fa-calendar-alt"></i> Mês
                            </button>
                            <button id="btn_pontos_outro" class="filter-button btn-light">
                                <i class="fa fa-calendar-plus"></i> Personalizado
                            </button>

                            <div id="div-filter-pontuacao" style="position: fixed; z-index: 9999; margin-top: 40px; right: 20px; width: 250px" class="dashboard-card card-light p-3 d-none shadow">
                                <div class="form-group mb-2">
                                    <label for="input-datai-pontos" class="small font-weight-bold">Data Inicial</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-datai-pontos" class="form-control" type="date" />
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="input-dataf-pontos" class="small font-weight-bold">Data Final</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-dataf-pontos" class="form-control" type="date" />
                                    </div>
                                </div>

                                <button id="btn-filter-pontos-data" class="btn btn-primary btn-block">
                                    <i class="fa fa-filter mr-1"></i> Filtrar
                                </button>
                            </div>
                        </div>

                        @if(Session::get('logado') == 'gestor')
                            <div class="form-group mb-3">
                                <label for="clientes_id_pontuacao" class="small font-weight-bold">Cliente</label>
                                <select id="clientes_id_pontuacao" name="clientes_id" class="form-control form-control-sm" required="required">
                                    <option value="">-- Todos os clientes --</option>
                                    @foreach($clientes as $cliente)
                                        <option value="{{$cliente->id}}">
                                            {{$cliente->nome}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <div class="table-scrollable border-0">
                            <table class="dashboard-table table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 70%;">Atendente</th>
                                        <th style="width: 30%; text-align: right">Pontos</th>
                                    </tr>
                                </thead>
                                <tbody id="dados_pontos_atendente">
                                    @php
                                        $totalPontos = 0;
                                        foreach($pontosatendentes as $cad) {
                                            $totalPontos += intval($cad['qtd']);
                                        }

                                        // Convert to array if it's a collection and sort by points in descending order
                                        $sortedPontosAtendentes = is_array($pontosatendentes) ? $pontosatendentes : $pontosatendentes->toArray();
                                        usort($sortedPontosAtendentes, function($a, $b) {
                                            return intval($b['qtd']) - intval($a['qtd']);
                                        });
                                    @endphp

                                    @foreach($sortedPontosAtendentes as $cad)
                                        @php
                                            $percentagem = $totalPontos > 0 ? (intval($cad['qtd']) / $totalPontos) * 100 : 0;
                                            $isTopPerformer = $percentagem > 25;
                                        @endphp
                                        <tr class="{{ $isTopPerformer ? 'table-success' : '' }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: {{ $isTopPerformer ? 'var(--secondary-color)' : '#6c757d' }}; color: white; border-radius: 4px;">
                                                        <i class="fa {{ $isTopPerformer ? 'fa-award' : 'fa-user' }} fa-sm"></i>
                                                    </span>
                                                    <span class="font-weight-medium" style="color: var(--text-color)">{{$cad['nome'] ? $cad['nome'] : 'Não informado'}}</span>
                                                </div>
                                            </td>
                                            <td style="text-align: right">
                                                <div class="d-flex flex-column align-items-end">
                                                    <span class="badge {{ $isTopPerformer ? 'badge-success' : 'badge-secondary' }} p-2">{{number_format(intval($cad['qtd']), 0, '', '.')}}</span>
                                                    @if($totalPontos > 0)
                                                        <small class="text-muted mt-1">{{number_format($percentagem, 1)}}%</small>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="bg-light font-weight-bold">
                                        <td>TOTAL</td>
                                        <td style="text-align: right">{{number_format($totalPontos, 0, '', '.')}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="row">
            <!-- Resumo de combustíveis -->
            <div class="col-md-6 px-2">
                <div class="dashboard-card shadow h-100" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4><i class="fa fa-gas-pump" style="color: #435887;"></i><span class="ml-2" id="txt-pontuacao">Resumo de combustíveis</span></h4>
                        <div class="chart-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary chart-action-btn dropdown-toggle" type="button" id="dropdownCombustiveis" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Exportar dados" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                    <i class="fa fa-download"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownCombustiveis">
                                    <a class="dropdown-item" href="#" id="download-combustiveis-chart">
                                        <i class="fa fa-image mr-2"></i> Baixar PNG
                                    </a>
                                    <a class="dropdown-item" href="#" id="export-combustiveis-csv">
                                        <i class="fa fa-file-csv mr-2"></i> Exportar CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card-body" style="background: white !important;">
                        <div class="filter-buttons mb-3">
                            <button id="btn_res_comb_hoje" class="filter-button btn-light">
                                <i class="fa fa-calendar-day"></i> Hoje
                            </button>
                            <button id="btn_res_comb_semana" class="filter-button btn-light">
                                <i class="fa fa-calendar-week"></i> Semana
                            </button>
                            <button id="btn_res_comb_mes" class="filter-button btn-light btn-select">
                                <i class="fa fa-calendar-alt"></i> Mês
                            </button>
                            <button id="btn_res_comb_outro" class="filter-button btn-light">
                                <i class="fa fa-calendar-plus"></i> Outro
                            </button>

                            <div id="div-filter-res-comb" style="position: fixed; z-index: 9999; margin-top: 40px; right: 20px; width: 250px" class="dashboard-card card-light p-3 d-none shadow">
                                <div class="form-group mb-2">
                                    <label for="input-datai-res-comb" class="small font-weight-bold">Data Inicial</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-datai-res-comb" class="form-control" type="date" />
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="input-dataf-res-comb" class="small font-weight-bold">Data Final</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                                        </div>
                                        <input id="input-dataf-res-comb" class="form-control" type="date" />
                                    </div>
                                </div>

                                <button id="btn-filter-res-comb-data" class="btn btn-primary btn-block">
                                    <i class="fa fa-filter mr-1"></i> Filtrar
                                </button>
                            </div>
                        </div>

                        @if(Session::get('logado') == 'gestor')
                            <div class="form-group col-auto">
                                <select name="clientes_id" class="form-control form-control-sm" required="required">
                                    <option value="">-- Cliente --</option>
                                    @foreach($clientes as $cliente)
                                        <option value="{{$cliente->id}}">
                                            {{$cliente->nome}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <div class="fuel-summary mb-3">
                            <div class="total-fuel-container text-center p-2 mb-3" style="background: rgba(67, 88, 135, 0.05); border-radius: 8px; box-shadow: 0 8px 15px rgba(67, 88, 135, 0.1);">
                                <span class="total-fuel-label">Total de combustível vendido</span>
                                <div class="total-fuel-value" id="total-fuel-value">
                                    @php
                                        $totalLitros = 0;
                                        foreach($resumo_combustiveis as $resumo) {
                                            $totalLitros += $resumo->qtd;
                                        }
                                    @endphp
                                    <span class="font-weight-bold" style="font-size: 1.5rem; color: #435887;">{{number_format($totalLitros, 2, ',', '.')}} Litros</span>
                                </div>
                            </div>
                        </div>

                        <div class="table-scrollable">
                            <table class="dashboard-table table table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 50%;">Combustível</th>
                                        <th style="width: 30%; text-align: right;">Quantidade</th>
                                        <th style="width: 20%; text-align: center;">Gráfico</th>
                                    </tr>
                                </thead>
                                <tbody id="dados_res_combustivel">
                                    @foreach($resumo_combustiveis as $resumo)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @php
                                                        $iconClass = 'fa-gas-pump';
                                                        $iconColor = '#3F458C';

                                                        if (stripos($resumo->descricao, 'gasolina') !== false) {
                                                            $iconClass = 'fa-gas-pump';
                                                            $iconColor = '#dc3545';
                                                        } elseif (stripos($resumo->descricao, 'diesel') !== false) {
                                                            $iconClass = 'fa-truck';
                                                            $iconColor = '#6c757d';
                                                        } elseif (stripos($resumo->descricao, 'etanol') !== false || stripos($resumo->descricao, 'álcool') !== false || stripos($resumo->descricao, 'alcool') !== false) {
                                                            $iconClass = 'fa-leaf';
                                                            $iconColor = '#28a745';
                                                        } elseif (stripos($resumo->descricao, 'gnv') !== false || stripos($resumo->descricao, 'gás') !== false || stripos($resumo->descricao, 'gas') !== false) {
                                                            $iconClass = 'fa-fire';
                                                            $iconColor = '#17a2b8';
                                                        }
                                                    @endphp
                                                    <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: {{$iconColor}}; color: white; border-radius: 4px;">
                                                        <i class="fa {{$iconClass}} fa-sm"></i>
                                                    </span>
                                                    <span class="font-weight-medium">{{$resumo->descricao}}</span>
                                                </div>
                                            </td>
                                            <td style="text-align: right; font-weight: 500;">{{number_format($resumo->qtd, 2, ',', '.')}} L</td>
                                            <td>
                                                @php
                                                    $percentagem = ($resumo->qtd / $totalLitros) * 100;
                                                @endphp
                                                <div class="progress" style="height: 8px;">
                                                    <div class="progress-bar" role="progressbar" style="width: {{$percentagem}}%; background-color: {{$iconColor}};" aria-valuenow="{{$percentagem}}" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="dashboard-card-footer text-center text-muted" style="background: white !important; border-top: 1px solid rgba(0,0,0,0.05); font-size: 0.8rem;">
                        &nbsp;
                    </div>
                </div>
            </div>
            <!-- Fim Resumo de combustíveis -->

            <!-- WincashAi Card -->
            <div class="col-md-6 px-2">
                <div class="dashboard-card shadow h-100" style="background: linear-gradient(135deg, #3F458C 0%, #4c7cf3 100%); color: white;">
                    <div class="dashboard-card-header">
                        <h4><i class="fa fa-robot" style="color: #ffffff;"></i><span class="ml-2">WincashAi</span></h4>
                    </div>
                    <div class="dashboard-card-body d-flex flex-column justify-content-center align-items-center text-center p-4">
                        <div class="ai-icon mb-4">
                            <i class="fa fa-brain" style="font-size: 4rem; background: rgba(255,255,255,0.1); padding: 1.5rem; border-radius: 50%; box-shadow: 0 10px 20px rgba(0,0,0,0.1); color: #ffffff;"></i>
                        </div>
                        <h3 class="mb-3">Em breve o novo chat de inteligência artificial da WinCash BR</h3>
                        <p class="lead mb-4">Uma nova experiência para otimizar sua gestão e atendimento.</p>
                        <div class="ai-features d-flex justify-content-center flex-wrap">
                            <span class="badge badge-light m-1 p-2"><i class="fa fa-chart-line mr-1"></i> Análise de dados</span>
                            <span class="badge badge-light m-1 p-2"><i class="fa fa-comments mr-1"></i> Atendimento</span>
                            <span class="badge badge-light m-1 p-2"><i class="fa fa-lightbulb mr-1"></i> Insights</span>
                        </div>
                    </div>
                    <div class="dashboard-card-footer text-center" style="background: rgba(0,0,0,0.1);">
                        <small>Aguarde novidades em breve!</small>
                    </div>
                </div>
            </div>
            <!-- Fim WincashAi Card -->
        </div>

        <div class="row">
            <div class="col-lg-4 px-2">
                <div class="dashboard-card card-light chart-card premios-chart-card" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4><i class="fa fa-trophy" style="color: #435887;"></i> Prêmios</h4>
                        <div class="chart-actions">
                            <button class="btn btn-sm btn-outline-secondary chart-action-btn" id="download-premios-chart" title="Baixar gráfico" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                <i class="fa fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-card-body d-flex flex-column justify-content-center" style="background: white !important;">
                        <div class="text-center mb-4">
                            <div class="premio-value-container" style="background: rgba(67, 88, 135, 0.05); box-shadow: 0 8px 15px rgba(67, 88, 135, 0.1);">
                                <span class="premio-value">{{$premiosresgatados}}</span>
                                <div class="premio-label">Resgatados</div>
                            </div>
                        </div>

                        @php
                            $porcentagem = 0;
                            if($premiosresgatados > 0) {
                                $porcentagem = $premiosbaixados / $premiosresgatados * 100;
                            }
                        @endphp

                        <div class="premio-progress-container" style="background-color: rgba(67, 88, 135, 0.03); border: 1px solid rgba(67, 88, 135, 0.1);">
                            <div class="d-flex justify-content-between mb-2">
                                <p class="mb-0 font-weight-bold">Solicitado X Baixado</p>
                                <p class="mb-0 font-weight-bold">{{number_format($porcentagem, 0, '', '')}}%</p>
                            </div>
                            <div class="progress premio-progress mb-3">
                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     style="width: {{$porcentagem}}%"
                                     aria-valuenow="{{$porcentagem}}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <div class="d-flex justify-content-between font-weight-bold">
                                <span>Solicitado: {{$premiosresgatados}}</span>
                                <span>Baixado: {{$premiosbaixados}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-8 px-2">
                <div class="dashboard-card card-primary chart-card vendas-chart-card" style="background: white !important; background-image: none !important; color: var(--text-color);">
                    <div class="dashboard-card-header" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4><i class="fa fa-money-check-alt" style="color: #435887;"></i> <span style="color: #000;">Vendas</span></h4>
                        <div class="chart-actions">
                            <button class="btn btn-sm btn-outline-secondary chart-action-btn" id="download-vendas-chart" title="Baixar gráfico" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                <i class="fa fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-card-body" style="background: white !important;">
                        <script>
                            var grafico_dados_vendas = [];
                            var grafico_local_vendas = 'grafico_vendas';
                        </script>

                        <div class="vendas-chart-title" style="color: var(--text-color);">Evolução de Vendas nos Últimos Meses</div>
                        <div id="grafico_vendas" class="chart-container" style="background: white !important;"></div>

                        @php
                            $totalVendas = 0;
                            foreach($vendasmes as $venda) {
                                $totalVendas += $venda['valor'];
                            }
                            $mediaVendas = count($vendasmes) > 0 ? $totalVendas / count($vendasmes) : 0;
                        @endphp

                        <div class="d-flex justify-content-between mt-2 px-3">
                            <div class="text-center">
                                <div class="text-muted small">Total</div>
                                <div class="font-weight-bold" style="color: #435887;">R$ {{ number_format($totalVendas, 2, ',', '.') }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-muted small">Média Mensal</div>
                                <div class="font-weight-bold" style="color: #435887;">R$ {{ number_format($mediaVendas, 2, ',', '.') }}</div>
                            </div>
                        </div>

                        @foreach($vendasmes as $venda)
                            <div style="display: none">
                                {{$venda['valor']}} - {{$venda['mes']}}/{{$venda['ano']}}
                            </div>

                            <script>
                                grafico_dados_vendas.push([new Date({{$venda['ano']}}, {{$venda['mes']-1}}, 1),{{$venda['valor']}}]);
                            </script>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 px-2">
                <div class="dashboard-card card-light chart-card programa-fidelidade-card" style="background: white !important; background-image: none !important; border-left: none !important; border: none !important;">
                    <div class="dashboard-card-header" style="background: white !important; color: var(--text-color) !important; border-bottom: 1px solid rgba(0,0,0,0.05);">
                        <h4><i class="fa fa-users" style="color: #435887;"></i> Programa Fidelidade</h4>
                        <div class="chart-actions">
                            <button class="btn btn-sm btn-outline-secondary chart-action-btn" id="toggle-chart-type" title="Alternar tipo de gráfico" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                <i class="fa fa-chart-line"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary chart-action-btn" id="download-chart" title="Baixar gráfico" style="background-color: rgba(0,0,0,0.05); color: var(--text-color);">
                                <i class="fa fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-card-body p-0" style="background: white !important;">
                        <script>
                            var grafico_dados_programa = [];
                            var grafico_local_programa = 'grafico_programa';
                        </script>
                        <div id="grafico_programa" class="chart-container" style="background: white !important;"></div>

                        @foreach($resgatadosmes as $item)
                            <div style="display: none">
                                {{$item['qtd']}} - {{$item['mes']}}/{{$item['ano']}}
                            </div>

                            <script>
                                grafico_dados_programa.push([new Date({{$item['ano']}}, {{$item['mes']-1}}, 1),{{$item['qtd']}}, null, null]);
                            </script>
                        @endforeach

                        @foreach($adesoesmes as $item)
                            <div style="display: none">
                                {{$item['qtd']}} - {{$item['mes']}}/{{$item['ano']}}
                            </div>

                            <script>
                                grafico_dados_programa.push([new Date({{$item['ano']}}, {{$item['mes']-1}}, 1), null, {{$item['qtd']}} , null]);
                            </script>
                        @endforeach

                        @foreach($pontuadoresmes as $item)
                            <div style="display: none">
                                {{$item['qtd']}} - {{$item['mes']}}/{{$item['ano']}}
                            </div>

                            <script>
                                grafico_dados_programa.push([new Date({{$item['ano']}}, {{$item['mes']-1}}, 1), null, null, {{$item['qtd']}}]);
                            </script>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        google.charts.load('current', {packages: ['corechart', 'bar']});

        google.charts.setOnLoadCallback(desenhavendasmes);
        google.charts.setOnLoadCallback(desenhaprograma);

        function desenhavendasmes() {
            drawMultSeries(grafico_dados_vendas, grafico_local_vendas, 'coluna', 'date');
        }

        function desenhaprograma() {
            drawMultSeries(grafico_dados_programa, grafico_local_programa, 'linha', 'date');
        }

        function drawMultSeries(dados, local, tipo = 'linha', base = 'date') {
            if (tipo == 'linha') {
                var data = new google.visualization.DataTable();
                data.addColumn(base);
                data.addColumn('number', 'Resgatados');
                data.addColumn('number', 'Adesões');
                data.addColumn('number', 'Pontuadores');

                data.addRows(dados);

                var options = {
                    legend: {
                        position: 'bottom',
                        alignment: 'center',
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 13,
                            bold: true
                        }
                    },
                    chartArea: {
                        width: '95%',
                        height: '80%',
                        left: '5%',
                        top: '5%'
                    },
                    backgroundColor: 'white',
                    hAxis: {
                        format: 'MMM/yy',
                        gridlines: {
                            color: 'rgba(200, 200, 200, 0.2)',
                            count: 12
                        },
                        minorGridlines: {
                            color: 'transparent'
                        },
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 11,
                            color: '#555'
                        },
                        slantedText: true,
                        slantedTextAngle: 30
                    },
                    vAxis: {
                        gridlines: {
                            color: 'rgba(200, 200, 200, 0.2)',
                            count: 6
                        },
                        minorGridlines: {
                            color: 'transparent'
                        },
                        minValue: 0,
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 11,
                            color: '#555'
                        },
                        format: '#,###'
                    },
                    colors: ['#4DB287', '#3F458C', '#9DD872'],
                    lineWidth: 4,
                    pointSize: 8,
                    pointShape: 'circle',
                    curveType: 'function',
                    tooltip: {
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 12,
                            bold: true,
                            color: '#333'
                        },
                        showColorCode: true,
                        isHtml: true
                    },
                    crosshair: {
                        color: '#3F458C',
                        opacity: 0.3,
                        trigger: 'both'
                    },
                    animation: {
                        startup: true,
                        duration: 1200,
                        easing: 'out'
                    },
                    annotations: {
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 12,
                            color: '#555'
                        }
                    },
                    series: {
                        0: { // Resgatados
                            lineWidth: 4,
                            pointSize: 8,
                            pointShape: 'circle',
                            areaOpacity: 0.1
                        },
                        1: { // Adesões
                            lineWidth: 4,
                            pointSize: 8,
                            pointShape: 'diamond',
                            areaOpacity: 0.1
                        },
                        2: { // Pontuadores
                            lineWidth: 4,
                            pointSize: 8,
                            pointShape: 'triangle',
                            areaOpacity: 0.1
                        }
                    }
                };

                var chart = new google.visualization.LineChart(document.getElementById(local));
            }

            if (tipo == 'coluna') {
                var data = new google.visualization.DataTable();
                data.addColumn(base);
                data.addColumn('number', 'Valor');

                data.addRows(dados);

                var options = {
                    legend: {
                        position: 'none'
                    },
                    backgroundColor: 'white',
                    chartArea: {
                        width: '90%',
                        height: '80%',
                        left: '8%',
                        top: '5%'
                    },
                    hAxis: {
                        format: 'MMM/yy',
                        gridlines: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            count: 6
                        },
                        minorGridlines: {
                            color: 'transparent'
                        },
                        textStyle: {
                            color: '#555',
                            fontName: 'Poppins',
                            fontSize: 11,
                            bold: true
                        },
                        slantedText: true,
                        slantedTextAngle: 30,
                        baselineColor: 'rgba(0, 0, 0, 0.1)'
                    },
                    vAxis: {
                        gridlines: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            count: 5
                        },
                        minorGridlines: {
                            color: 'transparent'
                        },
                        minValue: 0,
                        textStyle: {
                            color: '#555',
                            fontName: 'Poppins',
                            fontSize: 11,
                            bold: true
                        },
                        format: 'R$ #,###',
                        baselineColor: 'rgba(0, 0, 0, 0.1)'
                    },
                    colors: ['#3F458B'], // Cor principal
                    bar: {
                        groupWidth: '65%',
                        stroke: 'none',
                        cornerRadius: 4
                    },
                    animation: {
                        startup: true,
                        duration: 1200,
                        easing: 'out'
                    },
                    tooltip: {
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 12,
                            bold: true,
                            color: '#333'
                        },
                        showColorCode: true,
                        isHtml: true
                    },
                    annotations: {
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 12,
                            color: '#555'
                        }
                    }
                };

                var chart = new google.visualization.ColumnChart(document.getElementById(local));

                // Formata os dados para o tooltip personalizado
                var formatter = new google.visualization.NumberFormat({
                    prefix: 'R$ ',
                    decimalSymbol: ',',
                    groupingSymbol: '.',
                    fractionDigits: 2
                });
                formatter.format(data, 1); // Formata a coluna de valores

                // Adiciona efeito de gradiente nas colunas após o desenho do gráfico
                google.visualization.events.addListener(chart, 'ready', function () {
                    // Aplica o gradiente às colunas
                    var gradientElements = document.querySelectorAll('#' + local + ' svg rect[fill="#3F458B"]');
                    Array.prototype.forEach.call(gradientElements, function(rect) {
                        rect.setAttribute('fill', 'url(#vendas-gradient)');

                        // Adiciona efeito de sombra
                        var filter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
                        filter.setAttribute('id', 'shadow-' + Math.random().toString(36).substr(2, 9));
                        filter.innerHTML = '<feDropShadow dx="0" dy="2" stdDeviation="3" flood-opacity="0.3" />';

                        var svgElement = document.querySelector('#' + local + ' svg');
                        if (!svgElement.querySelector('defs')) {
                            var defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
                            svgElement.insertBefore(defs, svgElement.firstChild);
                        }

                        svgElement.querySelector('defs').appendChild(filter);
                        rect.setAttribute('filter', 'url(#' + filter.getAttribute('id') + ')');

                        // Adiciona animação de hover
                        rect.addEventListener('mouseover', function() {
                            this.setAttribute('opacity', '0.8');
                            this.style.cursor = 'pointer';
                        });

                        rect.addEventListener('mouseout', function() {
                            this.setAttribute('opacity', '1');
                        });
                    });

                    // Cria o gradiente se ainda não existir
                    if (!document.getElementById('vendas-gradient-def')) {
                        var svgElement = document.querySelector('#' + local + ' svg');
                        var defs = svgElement.querySelector('defs') || document.createElementNS('http://www.w3.org/2000/svg', 'defs');

                        var gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
                        gradient.setAttribute('id', 'vendas-gradient');
                        gradient.setAttribute('x1', '0%');
                        gradient.setAttribute('y1', '0%');
                        gradient.setAttribute('x2', '0%');
                        gradient.setAttribute('y2', '100%');

                        var stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                        stop1.setAttribute('offset', '0%');
                        stop1.setAttribute('stop-color', '#3F458B');
                        stop1.setAttribute('stop-opacity', '1');

                        var stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                        stop2.setAttribute('offset', '100%');
                        stop2.setAttribute('stop-color', '#5C63A9');
                        stop2.setAttribute('stop-opacity', '0.8');

                        gradient.appendChild(stop1);
                        gradient.appendChild(stop2);
                        defs.appendChild(gradient);
                        defs.setAttribute('id', 'vendas-gradient-def');

                        if (!svgElement.querySelector('defs')) {
                            svgElement.insertBefore(defs, svgElement.firstChild);
                        }
                    }

                    // Melhora as linhas de grade
                    var gridLines = document.querySelectorAll('#' + local + ' svg line');
                    Array.prototype.forEach.call(gridLines, function(line) {
                        if (line.getAttribute('stroke') === 'rgba(0, 0, 0, 0.1)') {
                            line.setAttribute('stroke-dasharray', '3,3');
                            line.setAttribute('stroke-width', '0.8');
                        }
                    });
                });
            }

            if (tipo == 'pizza') {
                var options = {
                    backgroundColor: 'transparent',
                    chartArea: {
                        width: '90%',
                        height: '90%'
                    },
                    colors: ['#4DB287', '#3F458C', '#9DD872', '#f8c291', '#6a89cc'],
                    legend: {
                        position: 'right',
                        textStyle: {
                            fontName: 'Poppins',
                            fontSize: 12
                        }
                    },
                    pieSliceText: 'percentage',
                    pieHole: 0.4,
                    animation: {
                        startup: true,
                        duration: 1000,
                        easing: 'out'
                    }
                };

                var chart = new google.visualization.PieChart(document.getElementById(local));
            }

            chart.draw(data, options);
        }

        $(document).ready(function () {
            // Selecionar o botão "Mês" por padrão para o resumo de combustíveis

            // Add event handlers for client selectors
            $("#clientes_id_cadastro").on('change', function() {
                let filter = $(".filter-button.btn-select").attr("id").replace("btn_cad_", "").toUpperCase();
                let unidadeId = $("#unidades_id").val();
                getCadPorAtendente(filter, "", "", unidadeId, function (resp) {
                    ExibeCadPorAtendente(resp.cadastros);
                });
            });

            $("#clientes_id_pontuacao").on('change', function() {
                let filter = $(".filter-button.btn-select").attr("id").replace("btn_pontos_", "").toUpperCase();
                getPontuacaoPorAtendente(filter, "", "", function (resp) {
                    ExibePontuacaoPorAtendente(resp.pontuacao);
                });
            });
            $("#btn_res_comb_mes").addClass("btn-select");

            // Chart type toggle and download functionality
            let currentChartType = 'linha';

            $('#toggle-chart-type').on('click', function() {
                if (currentChartType === 'linha') {
                    currentChartType = 'area';
                    $(this).find('i').removeClass('fa-chart-line').addClass('fa-chart-area');
                    redrawProgramaChart('area');
                } else {
                    currentChartType = 'linha';
                    $(this).find('i').removeClass('fa-chart-area').addClass('fa-chart-line');
                    redrawProgramaChart('linha');
                }
            });

            // Function to download chart as image
            function downloadChartAsImage(chartId, filename) {
                const chartContainer = document.getElementById(chartId);
                const chartImage = chartContainer.getElementsByTagName('svg')[0];

                if (chartImage) {
                    // Create a canvas element
                    const canvas = document.createElement('canvas');
                    canvas.width = chartImage.width.baseVal.value;
                    canvas.height = chartImage.height.baseVal.value;

                    // Convert SVG to canvas
                    const ctx = canvas.getContext('2d');
                    const svgData = new XMLSerializer().serializeToString(chartImage);
                    const img = new Image();

                    img.onload = function() {
                        ctx.drawImage(img, 0, 0);

                        // Create download link
                        const downloadLink = document.createElement('a');
                        downloadLink.download = filename;
                        downloadLink.href = canvas.toDataURL('image/png');
                        downloadLink.click();
                    };

                    img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
                }
            }

            // Download Programa Fidelidade chart
            $('#download-chart').on('click', function() {
                downloadChartAsImage('grafico_programa', 'programa-fidelidade-chart.png');
            });

            // Download Vendas chart
            $('#download-vendas-chart').on('click', function() {
                downloadChartAsImage('grafico_vendas', 'vendas-chart.png');
            });

            // Download Premios chart (for the progress bar)
            $('#download-premios-chart').on('click', function() {
                // For the premios card, we'll capture the entire card as it doesn't have an SVG
                html2canvas(document.querySelector('.premios-chart-card')).then(canvas => {
                    const downloadLink = document.createElement('a');
                    downloadLink.download = 'premios-chart.png';
                    downloadLink.href = canvas.toDataURL('image/png');
                    downloadLink.click();
                });
            });

            // Download Combustiveis data
            $('#download-combustiveis-chart').on('click', function() {
                // For the combustiveis card, we'll capture the entire card
                html2canvas(document.querySelector('.dashboard-card:has(#txt-pontuacao)')).then(canvas => {
                    const downloadLink = document.createElement('a');
                    downloadLink.download = 'combustiveis-data.png';
                    downloadLink.href = canvas.toDataURL('image/png');
                    downloadLink.click();
                });
            });

            // Download Cadastros por atendente chart
            $('#download-cadastros-chart').on('click', function() {
                // Capturar o card inteiro de cadastros
                html2canvas(document.querySelector('.dashboard-card:has(#txt-cadastro)')).then(canvas => {
                    const downloadLink = document.createElement('a');
                    downloadLink.download = 'cadastros-por-atendente.png';
                    downloadLink.href = canvas.toDataURL('image/png');
                    downloadLink.click();
                });
            });

            // Download Pontuação por atendente chart
            $('#download-pontuacao-chart').on('click', function() {
                // Capturar o card inteiro de pontuação
                html2canvas(document.querySelector('.dashboard-card:has(#txt-pontuacao)')).then(canvas => {
                    const downloadLink = document.createElement('a');
                    downloadLink.download = 'pontuacao-por-atendente.png';
                    downloadLink.href = canvas.toDataURL('image/png');
                    downloadLink.click();
                });
            });

            function redrawProgramaChart(type) {
                if (type === 'area') {
                    var data = new google.visualization.DataTable();
                    data.addColumn('date');
                    data.addColumn('number', 'Resgatados');
                    data.addColumn('number', 'Adesões');
                    data.addColumn('number', 'Pontuadores');

                    data.addRows(grafico_dados_programa);

                    var options = {
                        legend: {
                            position: 'bottom',
                            alignment: 'center',
                            textStyle: {
                                fontName: 'Poppins',
                                fontSize: 13,
                                bold: true
                            }
                        },
                        chartArea: {
                            width: '88%',
                            height: '78%',
                            left: '8%',
                            top: '5%'
                        },
                        backgroundColor: 'white',
                        hAxis: {
                            format: 'MMM/yy',
                            gridlines: {
                                color: 'rgba(200, 200, 200, 0.2)',
                                count: 12
                            },
                            minorGridlines: {
                                color: 'transparent'
                            },
                            textStyle: {
                                fontName: 'Poppins',
                                fontSize: 11,
                                color: '#555'
                            },
                            slantedText: true,
                            slantedTextAngle: 30
                        },
                        vAxis: {
                            gridlines: {
                                color: 'rgba(200, 200, 200, 0.2)',
                                count: 6
                            },
                            minorGridlines: {
                                color: 'transparent'
                            },
                            minValue: 0,
                            textStyle: {
                                fontName: 'Poppins',
                                fontSize: 11,
                                color: '#555'
                            },
                            format: '#,###'
                        },
                        colors: ['#4DB287', '#3F458C', '#9DD872'],
                        lineWidth: 2,
                        pointSize: 6,
                        areaOpacity: 0.2,
                        curveType: 'function',
                        tooltip: {
                            textStyle: {
                                fontName: 'Poppins',
                                fontSize: 12,
                                bold: true,
                                color: '#333'
                            },
                            showColorCode: true,
                            isHtml: true
                        },
                        crosshair: {
                            color: '#3F458C',
                            opacity: 0.3,
                            trigger: 'both'
                        },
                        animation: {
                            startup: true,
                            duration: 1200,
                            easing: 'out'
                        }
                    };

                    var chart = new google.visualization.AreaChart(document.getElementById('grafico_programa'));
                    chart.draw(data, options);
                } else {
                    // Redraw as line chart (default)
                    drawMultSeries(grafico_dados_programa, grafico_local_programa, 'linha', 'date');
                }
            }

            $(document).delegate(".tipobusca", "change", function () {
                checked = $(this).prop("checked");
                if (!checked) {
                    $('#inputcadastros').hide();
                }
                if (checked) {
                    $('#inputcadastros').fadeIn();
                }
            })

            $('#btn-filter-res-comb-data').on('click', function () {
               const datai = $('#input-datai-res-comb').val()
               const dataf = $('#input-dataf-res-comb').val()
                let unidadeId = $("#unidades_id").val()

                getResPorCombustivel("", datai, dataf, unidadeId, function (resp) {
                    ExibeResCombustivel(resp.res_comb)
                    $("#div-filter-res-comb").toggleClass("d-none")
                })
            })

            $("#btn_res_comb_hoje").on('click', function () {
                // Remover a classe btn-select de todos os botões e adicionar a este
                $(".filter-buttons button").removeClass("btn-select");
                $(this).addClass("btn-select");

                let unidadeId = $("#unidades_id").val();

                getResPorCombustivel("HOJE", "", "", unidadeId, function (resp) {
                    ExibeResCombustivel(resp.res_comb);
                })
            })

            $("#btn_res_comb_semana").on('click', function () {
                // Remover a classe btn-select de todos os botões e adicionar a este
                $(".filter-buttons button").removeClass("btn-select");
                $(this).addClass("btn-select");

                let unidadeId = $("#unidades_id").val();
                getResPorCombustivel("SEMANA", "", "", unidadeId, function (resp) {
                    ExibeResCombustivel(resp.res_comb);
                })
            })

            $("#btn_res_comb_mes").on('click', function () {
                // Remover a classe btn-select de todos os botões e adicionar a este
                $(".filter-buttons button").removeClass("btn-select");
                $(this).addClass("btn-select");

                let unidadeId = $("#unidades_id").val();
                getResPorCombustivel("MES", "", "", unidadeId, function (resp) {
                    ExibeResCombustivel(resp.res_comb);
                })
            })

            $("#btn_res_comb_outro").on('click', function () {
                // Remover a classe btn-select de todos os botões e adicionar a este
                $(".filter-buttons button").removeClass("btn-select");
                $(this).addClass("btn-select");

                $("#div-filter-res-comb").toggleClass("d-none");
            })

            // Adicionar classe btn-select ao botão de filtro personalizado quando clicado
            $("#btn-filter-res-comb-data").on('click', function() {
                $(".filter-buttons button").removeClass("btn-select");
                $("#btn_res_comb_outro").addClass("btn-select");
            })

            $("#btn_pontos_hoje").on('click', function () {
                $("#btn_pontos_hoje").addClass("btn-select")
                $("#btn_pontos_semana").removeClass("btn-select")
                $("#btn_pontos_mes").removeClass("btn-select")

                getPontuacaoPorAtendente("HOJE", "", "", function (resp) {
                    ExibePontuacaoPorAtendente(resp.pontuacao)
                    $("#txt-pontuacao").html("Pontuação por atendente ")
                })
            })

            $("#btn_pontos_semana").on('click', function () {
                $("#btn_pontos_semana").addClass("btn-select")
                $("#btn_pontos_hoje").removeClass("btn-select")
                $("#btn_pontos_mes").removeClass("btn-select")

                getPontuacaoPorAtendente("SEMANA", "", "", function (resp) {
                    ExibePontuacaoPorAtendente(resp.pontuacao)
                    $("#txt-pontuacao").html("Pontuação por atendente ")
                })
            })

            $("#btn_pontos_mes").on('click', function () {
                $("#btn_pontos_mes").addClass("btn-select")
                $("#btn_pontos_hoje").removeClass("btn-select")
                $("#btn_pontos_semana").removeClass("btn-select")

                getPontuacaoPorAtendente("MES", "", "", function (resp) {
                    ExibePontuacaoPorAtendente(resp.pontuacao)
                    $("#txt-pontuacao").html("Pontuação por atendente ")
                })
            })

            $("#btn_pontos_outro").on("click", function () {
                $("#div-filter-pontuacao").toggleClass("d-none")
            })

            $("#btn_cad_outro").on("click", function () {
                $("#div-filter-cadastro").toggleClass("d-none")
            })

            $("#btn-filter-pontos-data").on("click", function () {
                let datai = $("#input-datai-pontos").val();
                let dataf = $("#input-dataf-pontos").val();
                getPontuacaoPorAtendente("MES", datai, dataf, function (resp) {
                    ExibePontuacaoPorAtendente(resp.pontuacao)
                    $("#div-filter-pontuacao").toggleClass("d-none")
                    let xDatai = datai.toString().split("-")
                    xDatai = xDatai[2]+"/"+xDatai[1]+"/"+xDatai[0]

                    let xDataf = dataf.toString().split("-")
                    xDataf = xDataf[2]+"/"+xDataf[1]+"/"+xDataf[0]
                    $("#txt-pontuacao").html("Pontuação por atendente "+xDatai+" a "+xDataf)
                })
            })

            $("#btn-filter-cadastro-data").on("click", function () {
                let datai = $("#input-datai-cadastro").val();
                let dataf = $("#input-dataf-cadastro").val();
                let unidadeId = $("#unidades_id").val();

                getCadPorAtendente("MES", datai, dataf, unidadeId, function (resp) {
                    ExibeCadPorAtendente(resp.cadastros)
                    $("#div-filter-cadastro").toggleClass("d-none")
                    let xDatai = datai.toString().split("-")
                    xDatai = xDatai[2]+"/"+xDatai[1]+"/"+xDatai[0]

                    let xDataf = dataf.toString().split("-")
                    xDataf = xDataf[2]+"/"+xDataf[1]+"/"+xDataf[0]
                    $("#txt-cadastro").html("Cadastros por atendente "+xDatai+" a "+xDataf)
                })
            })

            $("#btn_cad_hoje").on('click', function () {

                $("#btn_cad_hoje").addClass("btn-select")
                $("#btn_cad_semana").removeClass("btn-select")
                $("#btn_cad_mes").removeClass("btn-select")
                let unidadeId = $("#unidades_id").val();

                getCadPorAtendente("HOJE", "", "", unidadeId, function (resp) {
                    ExibeCadPorAtendente(resp.cadastros)
                    $("#txt-cadastro").html("Cadastros por atendente ")
                })
            })

            $("#btn_cad_semana").on('click', function () {

                $("#btn_cad_semana").addClass("btn-select")
                $("#btn_cad_hoje").removeClass("btn-select")
                $("#btn_cad_mes").removeClass("btn-select")
                let unidadeId = $("#unidades_id").val();

                getCadPorAtendente("SEMANA", "", "", unidadeId, function (resp) {
                    ExibeCadPorAtendente(resp.cadastros)
                    $("#txt-cadastro").html("Cadastros por atendente ")
                })
            })

            $("#btn_cad_mes").on('click', function () {

                $("#btn_cad_mes").addClass("btn-select")
                $("#btn_cad_semana").removeClass("btn-select")
                $("#btn_cad_hoje").removeClass("btn-select")
                let unidadeId = $("#unidades_id").val();

                getCadPorAtendente("MES", "", "", unidadeId, function (resp) {
                    ExibeCadPorAtendente(resp.cadastros)
                    $("#txt-cadastro").html("Cadastros por atendente ")
                })
            })

            function getPontuacaoPorAtendente(filter = "", datai = "", dataf = "", callBack) {
                let params = {
                    filter: filter,
                    datai: datai,
                    dataf: dataf,
                    cliente_id: $("#clientes_id_pontuacao").val(),
                    unidade_id: $("#unidades_id").val()
                };

                $.ajax({
                    url: "/admin/pontuacaoporatendente",
                    type: 'get',
                    dataType: "json",
                    data: params,
                    success: function(resp) {
                        if (resp.error == "0") {
                            if (typeof callBack === 'function') {
                                callBack(resp);
                            } else {
                                ExibePontuacaoPorAtendente(resp.pontuacao);
                            }
                        } else {
                            alert(resp.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Erro ao buscar pontuação por atendente:", error);
                        alert("Ocorreu um erro ao buscar os dados. Por favor, tente novamente.");
                    }
                });
            }

            function getResPorCombustivel(filter, datai, dataf, unidadeId, callBack) {
                $.ajax({
                    url: window.origin + "/admin/ResumoPorCombustivel",
                    type: 'get',
                    dataType: "json",
                    data: {filter: filter, datai: datai, dataf: dataf, unidade_id: unidadeId},
                    success: function (resp) {
                        callBack(resp)
                    }
                })
            }

            function getCadPorAtendente(filter, datai, dataf, unidadeId, callBack) {
                let clienteId = $("#clientes_id_cadastro").val();
                $.ajax({
                    url: window.origin + "/admin/cadastroporatendente",
                    type: 'get',
                    dataType: "json",
                    data: {
                        filter: filter,
                        datai: datai,
                        dataf: dataf,
                        unidade_id: unidadeId,
                        cliente_id: clienteId
                    },
                    success: function (resp) {
                        callBack(resp);
                    }
                });
            }

            function ExibeResCombustivel(dados) {
                let html = "";
                let combustivel = "";
                let volume = "";
                let totalLitros = 0;

                // Calcular total de litros
                for (i in dados) {
                    totalLitros += parseFloat(dados[i].qtd);
                }

                // Atualizar o total de litros no topo
                $('#total-fuel-value').html('<span class="font-weight-bold" style="font-size: 1.5rem; color: #435887;">' +
                    new Intl.NumberFormat('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}).format(totalLitros) + ' Litros</span>');

                html += '<table class="dashboard-table table table-hover">';
                html += '<thead><tr><th style="width: 50%;">Combustível</th><th style="width: 30%; text-align: right;">Quantidade</th><th style="width: 20%; text-align: center;">Gráfico</th></tr></thead>';
                html += '<tbody>';

                for (i in dados) {
                    combustivel = "Não informado";
                    let iconClass = 'fa-gas-pump';
                    let iconColor = '#3F458C';

                    if (dados[i].descricao != null) {
                        combustivel = dados[i].descricao;

                        // Determinar ícone e cor com base no tipo de combustível
                        if (combustivel.toLowerCase().includes('gasolina')) {
                            iconClass = 'fa-gas-pump';
                            iconColor = '#dc3545';
                        } else if (combustivel.toLowerCase().includes('diesel')) {
                            iconClass = 'fa-truck';
                            iconColor = '#6c757d';
                        } else if (combustivel.toLowerCase().includes('etanol') ||
                                  combustivel.toLowerCase().includes('álcool') ||
                                  combustivel.toLowerCase().includes('alcool')) {
                            iconClass = 'fa-leaf';
                            iconColor = '#28a745';
                        } else if (combustivel.toLowerCase().includes('gnv') ||
                                  combustivel.toLowerCase().includes('gás') ||
                                  combustivel.toLowerCase().includes('gas')) {
                            iconClass = 'fa-fire';
                            iconColor = '#17a2b8';
                        }
                    }

                    const options = {minimumFractionDigits: 2, maximumFractionDigits: 2};
                    const numberFormat = new Intl.NumberFormat('pt-BR', options);
                    volume = parseFloat(dados[i].qtd);
                    const percentagem = (volume / totalLitros) * 100;

                    html += '<tr>';
                    html += '    <td>';
                    html += '        <div class="d-flex align-items-center">';
                    html += '            <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: ' + iconColor + '; color: white; border-radius: 4px;">';
                    html += '                <i class="fa ' + iconClass + ' fa-sm"></i>';
                    html += '            </span>';
                    html += '            <span class="font-weight-medium">' + combustivel + '</span>';
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td style="text-align: right; font-weight: 500;">' + numberFormat.format(volume) + ' L</td>';
                    html += '    <td>';
                    html += '        <div class="progress" style="height: 8px;">';
                    html += '            <div class="progress-bar" role="progressbar" style="width: ' + percentagem + '%; background-color: ' + iconColor + ';" aria-valuenow="' + percentagem + '" aria-valuemin="0" aria-valuemax="100"></div>';
                    html += '        </div>';
                    html += '    </td>';
                    html += '</tr>';
                }

                html += '</tbody>';
                html += '</table>';
                $('#dados_res_combustivel').html(html);
            }

            function ExibePontuacaoPorAtendente(dados) {
                let html = "";
                let nomeAtendente = "";
                let total = 0;

                // Verificar se dados existe e é um array
                if (!dados || !Array.isArray(dados)) {
                    dados = [];
                }

                // Sort data by quantity in descending order
                dados.sort(function(a, b) {
                    return parseInt(b.qtd) - parseInt(a.qtd);
                });

                // Calculate total
                for (i in dados) {
                    total += parseInt(dados[i].qtd);
                }

                // Generate table rows
                for (i in dados) {
                    nomeAtendente = dados[i].nome != null ? dados[i].nome : "Não informado";

                    // Calculate percentage and determine if top performer
                    let percentagem = total > 0 ? (parseInt(dados[i].qtd) / total) * 100 : 0;
                    let isTopPerformer = percentagem > 25;

                    html += '<tr>';
                    html += '    <td>';
                    html += '        <div class="d-flex align-items-center">';
                    html += '            <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: ' + (isTopPerformer ? 'var(--secondary-color)' : '#6c757d') + '; color: white; border-radius: 4px;">';
                    html += '                <i class="fa ' + (isTopPerformer ? 'fa-award' : 'fa-user') + ' fa-sm"></i>';
                    html += '            </span>';
                    html += '            <span class="font-weight-medium" style="color: var(--text-color)">' + nomeAtendente + '</span>';
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td style="text-align: right">';
                    html += '        <div class="d-flex flex-column align-items-end">';
                    html += '            <span class="badge ' + (isTopPerformer ? 'badge-success' : 'badge-secondary') + ' p-2">' + parseInt(dados[i].qtd).toLocaleString('pt-BR') + '</span>';
                    if (total > 0) {
                        html += '        <small class="text-muted mt-1">' + percentagem.toFixed(1) + '%</small>';
                    }
                    html += '        </div>';
                    html += '    </td>';
                    html += '</tr>';
                }

                // Add total row
                html += '<tr class="bg-light font-weight-bold">';
                html += '    <td>TOTAL</td>';
                html += '    <td style="text-align: right">' + total.toLocaleString('pt-BR') + '</td>';
                html += '</tr>';

                $('#dados_pontos_atendente').html(html);
            }

            function ExibeCadPorAtendente(dados) {
                let html = "";
                let nomeAtendente = "";
                let total = 0;

                // Sort data by quantity in descending order
                dados.sort(function(a, b) {
                    return parseInt(b.qtd) - parseInt(a.qtd);
                });

                // Calculate total
                for (i in dados) {
                    total += parseInt(dados[i].qtd);
                }

                // Generate table rows
                for (i in dados) {
                    nomeAtendente = dados[i].nome != null ? dados[i].nome : "Não informado";

                    // Calculate percentage and determine if top performer
                    let percentagem = total > 0 ? (parseInt(dados[i].qtd) / total) * 100 : 0;
                    let isTopPerformer = percentagem > 30;

                    html += '<tr>';
                    html += '    <td>';
                    html += '        <div class="d-flex align-items-center">';
                    html += '            <span class="mr-2" style="width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; background-color: ' + (isTopPerformer ? 'var(--secondary-color)' : '#6c757d') + '; color: white; border-radius: 4px;">';
                    html += '                <i class="fa ' + (isTopPerformer ? 'fa-award' : 'fa-user') + ' fa-sm"></i>';
                    html += '            </span>';
                    html += '            <span class="font-weight-medium" style="color: var(--text-color)">' + nomeAtendente + '</span>';
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td style="text-align: right">';
                    html += '        <div class="d-flex flex-column align-items-end">';
                    html += '            <span class="badge ' + (isTopPerformer ? 'badge-success' : 'badge-secondary') + ' p-2">' + parseInt(dados[i].qtd).toLocaleString('pt-BR') + '</span>';
                    if (total > 0) {
                        html += '        <small class="text-muted mt-1">' + percentagem.toFixed(1) + '%</small>';
                    }
                    html += '        </div>';
                    html += '    </td>';
                    html += '</tr>';
                }

                // Add total row
                html += '<tr class="bg-light font-weight-bold">';
                html += '    <td>TOTAL</td>';
                html += '    <td style="text-align: right">' + total.toLocaleString('pt-BR') + '</td>';
                html += '</tr>';

                $('#dados_cad_atendente').html(html);
            }

            $(document).delegate("#cadastros_id", "change", function () {
                val = $(this).val();
                $('#cadastronacional').val(val);
                $('#cadastronacional').hide();
                $('#cadastronacional').fadeIn('slow');
            });


        });

    </script>

@stop
