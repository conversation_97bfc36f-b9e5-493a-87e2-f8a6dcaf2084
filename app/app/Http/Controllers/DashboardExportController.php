<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Consumidores;
use App\Pontos;
use App\Abastecimento;
use App\Vendedores;
use App\Clientes;
use App\Unidades;
use DB;

class DashboardExportController extends Controller
{
    /**
     * Exportar cadastros por atendente para CSV
     */
    public function exportarCadastros(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');
        $cliente_id = $request->get('cliente_id', '');

        // Buscar dados dos cadastros por atendente
        $dados = $this->getCadastrosPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Atendente', 'Quantidade de Cadastros', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item['qtd'] / $total) * 100, 2) : 0;
            $csvData[] = [
                $item['nome'] ?? 'Não informado',
                $item['qtd'],
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', $total, '100%'];

        return $this->downloadCSV($csvData, 'cadastros-por-atendente-' . date('d-m-Y') . '.csv');
    }

    /**
     * Exportar pontuação por atendente para CSV
     */
    public function exportarPontuacao(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');
        $cliente_id = $request->get('cliente_id', '');

        // Buscar dados da pontuação por atendente
        $dados = $this->getPontuacaoPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Atendente', 'Pontos', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item['qtd'] / $total) * 100, 2) : 0;
            $csvData[] = [
                $item['nome'] ?? 'Não informado',
                number_format($item['qtd'], 0, ',', '.'),
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', number_format($total, 0, ',', '.'), '100%'];

        return $this->downloadCSV($csvData, 'pontuacao-por-atendente-' . date('d-m-Y') . '.csv');
    }

    /**
     * Exportar resumo de combustíveis para CSV
     */
    public function exportarCombustiveis(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');

        // Buscar dados do resumo de combustíveis
        $dados = $this->getResumoCombustiveis($filter, $datai, $dataf, $unidade_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Combustível', 'Quantidade (Litros)', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item['qtd'] / $total) * 100, 2) : 0;
            $csvData[] = [
                $item['descricao'] ?? 'Não informado',
                number_format($item['qtd'], 2, ',', '.'),
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', number_format($total, 2, ',', '.'), '100%'];

        return $this->downloadCSV($csvData, 'resumo-combustiveis-' . date('d-m-Y') . '.csv');
    }

    /**
     * Função auxiliar para download de CSV
     */
    private function downloadCSV($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Adicionar BOM para UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            foreach ($data as $row) {
                fputcsv($file, $row, ';'); // Usar ponto e vírgula como separador
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Buscar cadastros por atendente
     */
    private function getCadastrosPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id)
    {
        // Implementar lógica similar ao método existente
        // Por enquanto, retorno dados de exemplo
        return [
            ['nome' => 'João Silva', 'qtd' => 25],
            ['nome' => 'Maria Santos', 'qtd' => 18],
            ['nome' => 'Pedro Costa', 'qtd' => 12],
        ];
    }

    /**
     * Buscar pontuação por atendente
     */
    private function getPontuacaoPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id)
    {
        // Implementar lógica similar ao método existente
        // Por enquanto, retorno dados de exemplo
        return [
            ['nome' => 'João Silva', 'qtd' => 1250],
            ['nome' => 'Maria Santos', 'qtd' => 980],
            ['nome' => 'Pedro Costa', 'qtd' => 750],
        ];
    }

    /**
     * Buscar resumo de combustíveis
     */
    private function getResumoCombustiveis($filter, $datai, $dataf, $unidade_id)
    {
        // Implementar lógica similar ao método existente
        // Por enquanto, retorno dados de exemplo
        return [
            ['descricao' => 'Gasolina Comum', 'qtd' => 1500.50],
            ['descricao' => 'Etanol', 'qtd' => 800.25],
            ['descricao' => 'Diesel S10', 'qtd' => 1200.75],
        ];
    }
}
