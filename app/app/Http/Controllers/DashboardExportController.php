<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Consumidores;
use App\Pontos;
use App\Abastecimento;
use App\Vendedores;
use App\Clientes;
use App\Unidades;
use DB;

class DashboardExportController extends Controller
{
    /**
     * Exportar cadastros por atendente para CSV
     */
    public function exportarCadastros(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');
        $cliente_id = $request->get('cliente_id', '');

        // Verificar se usuário está logado como cliente
        if ($request->session()->get('logado') == 'cliente') {
            $cliente_id = $request->session()->get('logado_usuario')->clientes_id;
        }

        // Buscar dados dos cadastros por atendente
        $dados = $this->getCadastrosPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Atendente', 'Quantidade de Cadastros', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item->qtd / $total) * 100, 2) : 0;
            $csvData[] = [
                $item->nome ?? 'Não informado',
                $item->qtd,
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', $total, '100%'];

        return $this->downloadCSV($csvData, 'cadastros-por-atendente-' . date('d-m-Y') . '.csv');
    }

    /**
     * Exportar pontuação por atendente para CSV
     */
    public function exportarPontuacao(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');
        $cliente_id = $request->get('cliente_id', '');

        // Verificar se usuário está logado como cliente
        if ($request->session()->get('logado') == 'cliente') {
            $cliente_id = $request->session()->get('logado_usuario')->clientes_id;
        }

        // Buscar dados da pontuação por atendente
        $dados = $this->getPontuacaoPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Atendente', 'Pontos', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item->qtd / $total) * 100, 2) : 0;
            $csvData[] = [
                $item->nome ?? 'Não informado',
                number_format($item->qtd, 0, ',', '.'),
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', number_format($total, 0, ',', '.'), '100%'];

        return $this->downloadCSV($csvData, 'pontuacao-por-atendente-' . date('d-m-Y') . '.csv');
    }

    /**
     * Exportar resumo de combustíveis para CSV
     */
    public function exportarCombustiveis(Request $request)
    {
        $filter = $request->get('filter', 'MES');
        $datai = $request->get('datai', '');
        $dataf = $request->get('dataf', '');
        $unidade_id = $request->get('unidade_id', '');

        // Verificar se usuário está logado como cliente
        $cliente_id = '';
        if ($request->session()->get('logado') == 'cliente') {
            $cliente_id = $request->session()->get('logado_usuario')->clientes_id;
        }

        // Buscar dados do resumo de combustíveis
        $dados = $this->getResumoCombustiveis($filter, $datai, $dataf, $unidade_id, $cliente_id);

        // Preparar dados para CSV
        $csvData = [];
        $csvData[] = ['Combustível', 'Quantidade (Litros)', 'Percentual'];

        $total = array_sum(array_column($dados, 'qtd'));

        foreach ($dados as $item) {
            $percentual = $total > 0 ? round(($item->qtd / $total) * 100, 2) : 0;
            $csvData[] = [
                $item->descricao ?? 'Não informado',
                number_format($item->qtd, 2, ',', '.'),
                $percentual . '%'
            ];
        }

        // Adicionar linha de total
        $csvData[] = ['TOTAL', number_format($total, 2, ',', '.'), '100%'];

        return $this->downloadCSV($csvData, 'resumo-combustiveis-' . date('d-m-Y') . '.csv');
    }

    /**
     * Função auxiliar para download de CSV
     */
    private function downloadCSV($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Adicionar BOM para UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($data as $row) {
                fputcsv($file, $row, ';'); // Usar ponto e vírgula como separador
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Buscar cadastros por atendente
     */
    private function getCadastrosPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id)
    {
        // Definir datas baseado no filtro
        list($datai, $dataf) = $this->getDateRange($filter, $datai, $dataf);

        $query = DB::table('cadastros')
            ->select(DB::raw("count(cadastros.id) as qtd, vendedores.nome as nome"))
            ->leftJoin("vendedores", "cadastros.vendedores_id", "=", "vendedores.id")
            ->whereDate("cadastros.created_at", ">=", $datai)
            ->whereDate("cadastros.created_at", "<=", $dataf)
            ->whereRaw('vendedores.clientes_id = cadastros.clientes_id')
            ->groupBy("vendedores.nome")
            ->orderBy("qtd", "DESC");

        // Filtros adicionais
        if (!empty($cliente_id)) {
            $query->where("cadastros.clientes_id", $cliente_id);
        }

        if (!empty($unidade_id)) {
            $query->where("cadastros.unidades_id", $unidade_id);
        }

        return $query->get()->toArray();
    }

    /**
     * Buscar pontuação por atendente
     */
    private function getPontuacaoPorAtendente($filter, $datai, $dataf, $unidade_id, $cliente_id)
    {
        // Definir datas baseado no filtro
        list($datai, $dataf) = $this->getDateRange($filter, $datai, $dataf);

        $query = DB::table('pontos')
            ->select(DB::raw("sum(pontos.pontuacao) as qtd, vendedores.nome as nome"))
            ->leftJoin("vendedores", "pontos.vendedores_id", "=", "vendedores.id")
            ->whereBetween("dtcompra", [$datai, $dataf])
            ->whereRaw('vendedores.clientes_id = pontos.clientes_id')
            ->groupBy("vendedores.nome")
            ->orderBy("qtd", "DESC");

        // Filtros adicionais
        if (!empty($cliente_id)) {
            $query->where("pontos.clientes_id", $cliente_id);
        }

        if (!empty($unidade_id)) {
            $query->where("pontos.unidades_id", $unidade_id);
        }

        return $query->get()->toArray();
    }

    /**
     * Buscar resumo de combustíveis
     */
    private function getResumoCombustiveis($filter, $datai, $dataf, $unidade_id, $cliente_id = '')
    {
        // Definir datas baseado no filtro
        list($datai, $dataf) = $this->getDateRange($filter, $datai, $dataf);

        $query = DB::table("abastecimentos")
            ->leftJoin("bicos", "abastecimentos.cod_bico", "=", "bicos.cod_bico")
            ->leftJoin("produtos", "bicos.produto_id", "=", "produtos.id")
            ->selectRaw("round(sum(abastecimentos.qtd),2) as qtd, produtos.descricao as descricao")
            ->whereBetween("abastecimentos.data", [$datai, $dataf])
            ->whereRaw("abastecimentos.unidades_id = bicos.unidades_id")
            ->groupBy("produtos.descricao")
            ->orderBy("produtos.descricao");

        // Filtros adicionais
        if (!empty($cliente_id)) {
            $query->where("abastecimentos.clientes_id", $cliente_id);
        }

        if (!empty($unidade_id)) {
            $query->where("abastecimentos.unidades_id", $unidade_id);
        }

        return $query->get()->toArray();
    }

    /**
     * Definir range de datas baseado no filtro
     */
    private function getDateRange($filter, $datai, $dataf)
    {
        switch ($filter) {
            case 'HOJE':
                return [date('Y-m-d'), date('Y-m-d')];

            case 'SEMANA':
                $dataAtual = date('Y-m-d');
                $diaSemana = date("w", strtotime($dataAtual));
                while (intval($diaSemana) > 0) {
                    $dataAtual = date('Y-m-d', strtotime('-1 DAY', strtotime($dataAtual)));
                    $diaSemana = date("w", strtotime($dataAtual));
                }
                return [$dataAtual, date('Y-m-d')];

            case 'MES':
                return [date("Y-m-01"), date("Y-m-d")];

            default:
                if (!empty($datai) && !empty($dataf)) {
                    return [$datai, $dataf];
                }
                return [date("Y-m-01"), date("Y-m-d")];
        }
    }
}
